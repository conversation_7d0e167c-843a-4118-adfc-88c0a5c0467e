#!/usr/bin/env python3
"""
Quick fix script to add sample products and images to the marketplace
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
from datetime import datetime, timezone
import random

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import get_db

def create_sample_image(product_name, category, size=(400, 300)):
    """Create a simple sample image for a product"""
    # Create a new image with a colored background
    colors = {
        'pottery': '#8B4513',      # <PERSON>
        'textiles': '#4169E1',     # Royal Blue  
        'woodwork': '#D2691E',     # Chocolate
        'metalwork': '#708090',    # Slate Gray
        'jewelry': '#FFD700',      # Gold
        'paintings': '#9370DB'     # Medium Purple
    }
    
    bg_color = colors.get(category.lower(), '#696969')  # Default gray
    
    # Create image
    img = Image.new('RGB', size, color=bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("arial.ttf", 24)
        small_font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Add product name (wrap text if too long)
    words = product_name.split()
    lines = []
    current_line = []
    
    for word in words:
        current_line.append(word)
        line_text = ' '.join(current_line)
        bbox = draw.textbbox((0, 0), line_text, font=font)
        if bbox[2] > size[0] - 40:  # Leave 20px margin on each side
            if len(current_line) > 1:
                current_line.pop()
                lines.append(' '.join(current_line))
                current_line = [word]
            else:
                lines.append(word)
                current_line = []
    
    if current_line:
        lines.append(' '.join(current_line))
    
    # Calculate total text height
    total_height = len(lines) * 30 + 40  # 30px per line + category
    start_y = (size[1] - total_height) // 2
    
    # Draw text lines
    for i, line in enumerate(lines):
        bbox = draw.textbbox((0, 0), line, font=font)
        text_width = bbox[2] - bbox[0]
        x = (size[0] - text_width) // 2
        y = start_y + i * 30
        draw.text((x, y), line, fill='white', font=font)
    
    # Add category
    category_text = f"({category.title()})"
    bbox = draw.textbbox((0, 0), category_text, font=small_font)
    text_width = bbox[2] - bbox[0]
    x = (size[0] - text_width) // 2
    y = start_y + len(lines) * 30 + 10
    draw.text((x, y), category_text, fill='lightgray', font=small_font)
    
    return img

def add_sample_products():
    """Add sample products to the database"""
    
    sample_products = [
        {
            'name': 'Handcrafted Ceramic Bowl',
            'description': 'Beautiful handmade ceramic bowl with traditional glazing techniques.',
            'category': 'pottery',
            'price': 45.00,
            'artisan_id': 'elena_rodriguez',
            'artisan_name': 'Elena Rodriguez',
            'location': 'Santa Fe, New Mexico',
            'materials': ['Clay', 'Natural Glaze'],
            'dimensions': '8" diameter x 3" height',
            'weight': '1.2 lbs',
            'ai_quality_score': 0.92,
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Wooden Coffee Table',
            'description': 'Solid oak coffee table with hand-carved details and natural finish.',
            'category': 'woodwork',
            'price': 320.00,
            'artisan_id': 'marcus_chen',
            'artisan_name': 'Marcus Chen',
            'location': 'Portland, Oregon',
            'materials': ['Oak Wood', 'Natural Oil Finish'],
            'dimensions': '36" x 18" x 16"',
            'weight': '25 lbs',
            'ai_quality_score': 0.88,
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Woven Wool Scarf',
            'description': 'Soft merino wool scarf with traditional patterns in vibrant colors.',
            'category': 'textiles',
            'price': 75.00,
            'artisan_id': 'amara_okafor',
            'artisan_name': 'Amara Okafor',
            'location': 'Brooklyn, New York',
            'materials': ['Merino Wool', 'Natural Dyes'],
            'dimensions': '60" x 8"',
            'weight': '0.3 lbs',
            'ai_quality_score': 0.95,
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Silver Ring with Turquoise',
            'description': 'Sterling silver ring featuring a natural turquoise stone.',
            'category': 'jewelry',
            'price': 120.00,
            'artisan_id': 'david_silverstein',
            'artisan_name': 'David Silverstein',
            'location': 'Asheville, North Carolina',
            'materials': ['Sterling Silver', 'Turquoise'],
            'dimensions': 'Size 7 (adjustable)',
            'weight': '0.1 lbs',
            'ai_quality_score': 0.90,
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Abstract Canvas Painting',
            'description': 'Original abstract painting with bold colors and dynamic composition.',
            'category': 'paintings',
            'price': 250.00,
            'artisan_id': 'sarah_martinez',
            'artisan_name': 'Sarah Martinez',
            'location': 'Austin, Texas',
            'materials': ['Acrylic Paint', 'Canvas'],
            'dimensions': '16" x 20"',
            'weight': '2 lbs',
            'ai_quality_score': 0.87,
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Copper Wind Chimes',
            'description': 'Hand-forged copper wind chimes with melodious tones.',
            'category': 'metalwork',
            'price': 85.00,
            'artisan_id': 'robert_kim',
            'artisan_name': 'Robert Kim',
            'location': 'Seattle, Washington',
            'materials': ['Copper', 'Hemp Cord'],
            'dimensions': '24" length',
            'weight': '1.5 lbs',
            'ai_quality_score': 0.89,
            'created_at': datetime.now(timezone.utc)
        }
    ]
    
    return sample_products

def main():
    """Main function to fix marketplace issues"""
    print("🔧 Fixing CraftConnect Marketplace Issues")
    print("=" * 45)
    
    with app.app_context():
        try:
            db = get_db()
            
            # Clear existing products for fresh start
            print("🗑️ Clearing existing products...")
            db.products.delete_many({})
            
            # Add sample products
            print("📦 Adding sample products...")
            products = add_sample_products()
            result = db.products.insert_many(products)
            print(f"✅ Added {len(result.inserted_ids)} products")
            
            # Create uploads directory if it doesn't exist
            uploads_dir = os.path.join('static', 'uploads')
            if not os.path.exists(uploads_dir):
                os.makedirs(uploads_dir)
            
            # Create sample images
            print("🎨 Creating sample images...")
            created_images = 0
            
            for i, product in enumerate(products):
                try:
                    # Create image
                    img = create_sample_image(product['name'], product['category'])
                    
                    # Save image
                    filename = f"product_{i+1}_{product['category']}.jpg"
                    image_path = os.path.join(uploads_dir, filename)
                    img.save(image_path, 'JPEG', quality=85)
                    
                    # Update product with image path
                    web_image_path = f"/static/uploads/{filename}"
                    db.products.update_one(
                        {'name': product['name']},
                        {'$set': {'image_path': web_image_path}}
                    )
                    
                    print(f"✅ Created image for: {product['name']}")
                    created_images += 1
                    
                except Exception as e:
                    print(f"❌ Error creating image for {product['name']}: {str(e)}")
            
            print(f"\n🎉 Successfully fixed marketplace!")
            print(f"📦 Products added: {len(products)}")
            print(f"🖼️ Images created: {created_images}")
            print(f"🌐 Visit: http://127.0.0.1:5000/marketplace/")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
