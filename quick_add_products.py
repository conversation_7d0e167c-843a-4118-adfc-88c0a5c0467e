#!/usr/bin/env python3
"""
Quick script to add sample products to the marketplace
"""

import sys
import os
from datetime import datetime, timezone

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from database import get_db

def add_sample_products():
    """Add sample products to the database"""

    sample_products = [
        {
            'name': 'Handcrafted Ceramic Bowl',
            'description': 'Beautiful handmade ceramic bowl with traditional glazing techniques. Perfect for serving or display.',
            'category': 'pottery',
            'price': 45.00,
            'artisan_id': 'elena_rodriguez',
            'artisan_name': '<PERSON>',
            'location': 'Santa Fe, New Mexico',
            'materials': ['Clay', 'Natural Glaze'],
            'dimensions': '8" diameter x 3" height',
            'weight': '1.2 lbs',
            'ai_quality_score': 0.92,
            'image_path': 'https://via.placeholder.com/400x300/8B4513/FFFFFF?text=Ceramic+Bowl',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Wooden Coffee Table',
            'description': 'Solid oak coffee table with hand-carved details and natural finish. A centerpiece for any living room.',
            'category': 'woodwork',
            'price': 320.00,
            'artisan_id': 'marcus_chen',
            'artisan_name': 'Marcus Chen',
            'location': 'Portland, Oregon',
            'materials': ['Oak Wood', 'Natural Oil Finish'],
            'dimensions': '36" x 18" x 16"',
            'weight': '25 lbs',
            'ai_quality_score': 0.88,
            'image_path': 'https://via.placeholder.com/400x300/D2691E/FFFFFF?text=Coffee+Table',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Woven Wool Scarf',
            'description': 'Soft merino wool scarf with traditional patterns in vibrant colors. Handwoven with care.',
            'category': 'textiles',
            'price': 75.00,
            'artisan_id': 'amara_okafor',
            'artisan_name': 'Amara Okafor',
            'location': 'Brooklyn, New York',
            'materials': ['Merino Wool', 'Natural Dyes'],
            'dimensions': '60" x 8"',
            'weight': '0.3 lbs',
            'ai_quality_score': 0.95,
            'image_path': 'https://via.placeholder.com/400x300/4169E1/FFFFFF?text=Wool+Scarf',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Silver Ring with Turquoise',
            'description': 'Sterling silver ring featuring a natural turquoise stone. Handcrafted with traditional techniques.',
            'category': 'jewelry',
            'price': 120.00,
            'artisan_id': 'david_silverstein',
            'artisan_name': 'David Silverstein',
            'location': 'Asheville, North Carolina',
            'materials': ['Sterling Silver', 'Turquoise'],
            'dimensions': 'Size 7 (adjustable)',
            'weight': '0.1 lbs',
            'ai_quality_score': 0.90,
            'image_path': 'https://via.placeholder.com/400x300/FFD700/000000?text=Silver+Ring',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Abstract Canvas Painting',
            'description': 'Original abstract painting with bold colors and dynamic composition. A unique piece of art.',
            'category': 'paintings',
            'price': 250.00,
            'artisan_id': 'sarah_martinez',
            'artisan_name': 'Sarah Martinez',
            'location': 'Austin, Texas',
            'materials': ['Acrylic Paint', 'Canvas'],
            'dimensions': '16" x 20"',
            'weight': '2 lbs',
            'ai_quality_score': 0.87,
            'image_path': 'https://via.placeholder.com/400x300/9370DB/FFFFFF?text=Abstract+Art',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Copper Wind Chimes',
            'description': 'Hand-forged copper wind chimes with melodious tones. Perfect for garden or patio.',
            'category': 'metalwork',
            'price': 85.00,
            'artisan_id': 'robert_kim',
            'artisan_name': 'Robert Kim',
            'location': 'Seattle, Washington',
            'materials': ['Copper', 'Hemp Cord'],
            'dimensions': '24" length',
            'weight': '1.5 lbs',
            'ai_quality_score': 0.89,
            'image_path': 'https://via.placeholder.com/400x300/708090/FFFFFF?text=Wind+Chimes',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Leather Handbag',
            'description': 'Handstitched leather handbag with vintage brass hardware. Durable and stylish.',
            'category': 'textiles',
            'price': 180.00,
            'artisan_id': 'maria_santos',
            'artisan_name': 'Maria Santos',
            'location': 'Phoenix, Arizona',
            'materials': ['Genuine Leather', 'Brass Hardware'],
            'dimensions': '12" x 8" x 4"',
            'weight': '1.8 lbs',
            'ai_quality_score': 0.91,
            'image_path': 'https://via.placeholder.com/400x300/8B4513/FFFFFF?text=Leather+Bag',
            'created_at': datetime.now(timezone.utc)
        },
        {
            'name': 'Ceramic Vase Set',
            'description': 'Set of three ceramic vases in complementary sizes and glazes. Perfect for fresh or dried flowers.',
            'category': 'pottery',
            'price': 95.00,
            'artisan_id': 'elena_rodriguez',
            'artisan_name': 'Elena Rodriguez',
            'location': 'Santa Fe, New Mexico',
            'materials': ['Stoneware Clay', 'Ceramic Glaze'],
            'dimensions': 'Small: 4"H, Medium: 6"H, Large: 8"H',
            'weight': '3.5 lbs total',
            'ai_quality_score': 0.93,
            'image_path': 'https://via.placeholder.com/400x300/CD853F/FFFFFF?text=Vase+Set',
            'created_at': datetime.now(timezone.utc)
        }
    ]

    return sample_products

def main():
    """Main function to add sample products"""
    print("📦 Adding Sample Products to CraftConnect")
    print("=" * 40)

    with app.app_context():
        try:
            db = get_db()

            # Clear existing products for fresh start
            print("🗑️ Clearing existing products...")
            db.products.delete_many({})

            # Add sample products
            print("📦 Adding sample products...")
            products = add_sample_products()
            result = db.products.insert_many(products)
            print(f"✅ Added {len(result.inserted_ids)} products")

            print(f"\n🎉 Successfully added products to marketplace!")
            print(f"📦 Products added: {len(products)}")
            print(f"🌐 Visit: http://127.0.0.1:5000/marketplace/")
            print(f"🖼️ Images: Using placeholder images from placeholder.com")

        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
