{% extends "base.html" %}

{% block title %}Marketplace - CraftConnect{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Artisan Marketplace</h1>
            <p class="lead">Discover authentic handcrafted products from talented artisans around the world.</p>
            
            <!-- Search and Filter -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="Search products...">
                        <button class="btn btn-outline-secondary" type="button" id="searchBtn" aria-label="Search products">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select class="form-select" id="categoryFilter" aria-label="Filter products by category">
                        <option value="">All Categories</option>
                        <option value="pottery">Pottery</option>
                        <option value="textiles">Textiles</option>
                        <option value="woodwork">Woodwork</option>
                        <option value="metalwork">Metalwork</option>
                        <option value="jewelry">Jewelry</option>
                        <option value="paintings">Paintings</option>
                    </select>
                </div>
            </div>
            
            <!-- Products Grid -->
            <div class="row" id="productsGrid">
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading products...</p>
                </div>
            </div>
            
            <!-- No Products Message -->
            <div class="row d-none" id="noProducts">
                <div class="col-12 text-center">
                    <div class="card">
                        <div class="card-body">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h4>No Products Found</h4>
                            <p class="text-muted">Be the first to add a product to the marketplace!</p>
                            <a href="{{ url_for('upload.upload_product') }}" class="btn btn-primary">Add Product</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Details Modal -->
<div class="modal fade" id="productModal" tabindex="-1" aria-labelledby="productModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalLabel">Product Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <img id="modalProductImage" src="" alt="Product Image" class="img-fluid rounded" style="max-height: 300px; width: 100%; object-fit: cover;">
                    </div>
                    <div class="col-md-6">
                        <h4 id="modalProductName"></h4>
                        <p class="text-muted" id="modalProductDescription"></p>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>Price:</strong>
                                <h5 class="text-primary" id="modalProductPrice"></h5>
                            </div>
                            <div class="col-6">
                                <strong>Category:</strong>
                                <span class="badge bg-secondary" id="modalProductCategory"></span>
                            </div>
                        </div>
                        <hr>
                        <div class="mb-2">
                            <strong>Artisan:</strong> <span id="modalProductArtisan"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Location:</strong> <span id="modalProductLocation"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Materials:</strong> <span id="modalProductMaterials"></span>
                        </div>
                        <div class="mb-2">
                            <strong>Dimensions:</strong> <span id="modalProductDimensions"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Add to Cart</button>
            </div>
        </div>
    </div>
</div>

<script>
let allProducts = [];

document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    
    // Search functionality
    document.getElementById('searchBtn').addEventListener('click', filterProducts);
    document.getElementById('searchInput').addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            filterProducts();
        }
    });
    
    // Category filter
    document.getElementById('categoryFilter').addEventListener('change', filterProducts);
});

async function loadProducts() {
    try {
        const response = await fetch('/marketplace/api/products');
        allProducts = await response.json();
        displayProducts(allProducts);
    } catch (error) {
        console.error('Error loading products:', error);
        document.getElementById('productsGrid').innerHTML = `
            <div class="col-12 text-center">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Error loading products. Please try again later.
                </div>
            </div>
        `;
    }
}

function displayProducts(products) {
    const grid = document.getElementById('productsGrid');
    const noProducts = document.getElementById('noProducts');
    
    if (products.length === 0) {
        grid.classList.add('d-none');
        noProducts.classList.remove('d-none');
        return;
    }
    
    grid.classList.remove('d-none');
    noProducts.classList.add('d-none');
    
    const productsHtml = products.map(product => `
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                ${product.image_path ? `
                    <img src="${product.image_path}" class="card-img-top" alt="${product.name}" style="height: 200px; object-fit: cover;">
                ` : `
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-image fa-3x text-muted"></i>
                    </div>
                `}
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">${product.name}</h5>
                    <p class="card-text">${product.description || 'No description available'}</p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-secondary">${product.category || 'Uncategorized'}</span>
                            <h5 class="text-primary mb-0">$${product.price || '0'}</h5>
                        </div>
                        ${product.ai_quality_score ? `
                            <div class="mt-2">
                                <small class="text-muted">AI Quality Score: ${Math.round(product.ai_quality_score * 100)}%</small>
                            </div>
                        ` : ''}
                        <button class="btn btn-primary btn-sm mt-2 w-100" onclick="viewProductDetails('${product._id}', '${product.name}', '${product.description}', '${product.price}', '${product.category}', '${product.artisan_name || 'Unknown Artisan'}', '${product.location || 'Unknown Location'}', '${product.materials ? product.materials.join(", ") : "Not specified"}', '${product.dimensions || "Not specified"}')">View Details</button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
    
    grid.innerHTML = productsHtml;
}

function filterProducts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const category = document.getElementById('categoryFilter').value;
    
    let filteredProducts = allProducts.filter(product => {
        const matchesSearch = !searchTerm || 
            product.name.toLowerCase().includes(searchTerm) ||
            (product.description && product.description.toLowerCase().includes(searchTerm));
        
        const matchesCategory = !category || product.category === category;
        
        return matchesSearch && matchesCategory;
    });
    
    displayProducts(filteredProducts);
}

function viewProductDetails(id, name, description, price, category, artisan, location, materials, dimensions) {
    // Set modal content
    document.getElementById('modalProductName').textContent = name;
    document.getElementById('modalProductDescription').textContent = description || 'No description available';
    document.getElementById('modalProductPrice').textContent = '$' + (price || '0');
    document.getElementById('modalProductCategory').textContent = category || 'Uncategorized';
    document.getElementById('modalProductArtisan').textContent = artisan;
    document.getElementById('modalProductLocation').textContent = location;
    document.getElementById('modalProductMaterials').textContent = materials;
    document.getElementById('modalProductDimensions').textContent = dimensions;

    // Find the product image
    const product = allProducts.find(p => p._id === id);
    const modalImage = document.getElementById('modalProductImage');
    if (product && product.image_path) {
        modalImage.src = product.image_path;
        modalImage.alt = name;
    } else {
        modalImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
        modalImage.alt = 'No image available';
    }

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}
</script>
{% endblock %}
