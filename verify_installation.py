"""
Verify that all required packages are installed correctly
"""

def check_imports():
    try:
        import flask
        print("✓ Flask:", flask.__version__)
        
        import tensorflow as tf
        print("✓ TensorFlow:", tf.__version__)
        
        import cv2
        print("✓ OpenCV:", cv2.__version__)
        
        import numpy as np
        print("✓ NumPy:", np.__version__)
        
        import PIL
        print("✓ Pillow:", PIL.__version__)
        
        import pymongo
        print("✓ PyMongo:", pymongo.__version__)
        
        import sklearn
        print("✓ Scikit-learn:", sklearn.__version__)
        
        print("\n🎉 All packages installed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        return False

if __name__ == "__main__":
    check_imports()